from Helpers.LLMRequest import create_llm_client, ModelType

def SearchTermAgent(topic):
    llm = create_llm_client(
        system_instruction="""Your job is to create search terms based on a provided topic for a finance YouTube short channel.
The channel is provides quick insights into financial topics currently trending to help listeners make better financial decisions.
Main focus: Stock/Crypto Market.

You should make 3 search terms for each topic.

## Response Format
<terms><term>Topic 1</term><term>Topic 2</term><term>Topic 3</term></terms>
""",
        enable_tools=False,
        model=ModelType.GEMINI_2_5_FLASH_LIGHT
    )

    response = llm.generate_response(f"""You should make 3 search terms.

Topic to create search terms for:
{topic}
""")
    if response is None:
        return []

    # Parse response
    split_one = response.split("<terms>")[1]
    split_two = split_one.split("</terms>")[0]
    split_three = split_two.split("<term>")[1:]
    terms = [split_three[i].split("</term>")[0] for i in range(len(split_three))]

    return terms
