from Helpers.LLMRequest import create_llm_client, ModelType

def StoryPickerAgent(used_topics: list[str], topics: list[str]) -> list[str]:
    llm = create_llm_client(
        system_instruction=f"""Your job is to select topics for a finance YouTube short channel.
The channel is provides quick insights into financial topics currently trending to help listeners make better financial decisions.
Main focus: Stock/Crypto Market.

## Example good storys ideas:
- TSLA Today
- Whats happening with Bitcoin Today
- Stock Market Today
- Crypto Market Today
- NVIDIA Stock Price
- Stock Market Movers
- Crypto Market Movers
- High Yield Dividend Stocks

## Things to avoid:
- What are ... (index funds, divident stocks, etc)
- We want to try to get current movies in companies by following trends.

(since you dont have live market data dont do things like Why is ___ up today? etc)

## Selection Criteria
- Finance related
- Not too political
- Avoid bias
- Not already used today

You can create your own topics if none are found based in finance and similar to the examples above.

## Topics Used Today
{used_topics}

## Response Format
<topics><topic>Topic 1</topic><topic>Topic 2</topic><topic>Topic 3</topic></topics>
""",
        enable_tools=False,
        model=ModelType.GEMINI_2_5_FLASH_LIGHT
    )

    response = llm.generate_response(f"""Trending Topics:
{topics}
""")
    if response is None:
        return []

    # Parse response
    split_one = response.split("<topics>")[1]
    split_two = split_one.split("</topics>")[0]
    split_three = split_two.split("<topic>")[1:]
    topics = [split_three[i].split("</topic>")[0] for i in range(len(split_three))]

    return topics
