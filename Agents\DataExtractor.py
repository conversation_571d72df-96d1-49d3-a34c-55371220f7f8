from Helpers.LLMRequest import create_llm_client, ModelType
from Helpers.RetryMechanism import create_llm_retry_mechanism

def ExtractorAgent(topic, source_text):
    llm = create_llm_client(
        system_instruction=f"""Your job is to extract content based on the topic from a complete webpage text.
You should extract detailed information and present it in a structured format.

Topic: {topic}

## Response format if content found
<response>Your output
information goes here
can be multiline
</response>

## Response format if no helpful content found
<response>Not Found</response>

Make sure your response is within the <response> tag no matter what.
""",
        enable_tools=False,
        model=ModelType.GEMINI_2_5_FLASH
    )

    try:
        # Initialize retry mechanism for more reliable LLM calls
        retry_mechanism = create_llm_retry_mechanism()

        def generate_extraction_with_retry():
            return llm.generate_response(f"""Topic: {topic}

Source Text:
{source_text}
""")

        # Use retry mechanism for LLM call
        response = retry_mechanism.retry_operation(
            generate_extraction_with_retry,
            exception_types=(Exception,),
            operation_name=f"content_extraction_{topic[:30]}"
        )

        if response is None:
            return None

        # Parse response with error handling
        if "<response>" not in response or "</response>" not in response:
            print(f"⚠️  Invalid response format from LLM for topic '{topic}'")
            return None

        split_one = response.split("<response>")[1]
        split_two = split_one.split("</response>")[0].strip()

        if split_two == "Not Found" or not split_two:
            return None

        return split_two

    except Exception as e:
        print(f"❌ Error in ExtractorAgent for topic '{topic}': {str(e)}")
        return None
