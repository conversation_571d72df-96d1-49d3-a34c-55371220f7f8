from Helpers.LLMRequest import create_llm_client, ModelType

def ExtractorAgent(topic, source_text):
    llm = create_llm_client(
        system_instruction=f"""Your job is to extract content based on the topic from a complete webpage text.
You should extract detailed information and present it in a structured format.

Topic: {topic}

## Response format if content found
<response>Your output
information goes here
can be multiline
</response>

## Response format if no helpful content found
<response>Not Found</response>

Make sure your response is within the <response> tag no matter what.
""",
        enable_tools=False,
        model=ModelType.GEMINI_2_5_FLASH
    )

    response = llm.generate_response(f"""Topic: {topic}
    
Source Text:
{source_text}
""")
    if response is None:
        return []

    # Parse response
    split_one = response.split("<response>")[1]
    split_two = split_one.split("</response>")[0].strip()

    if split_two == "Not Found":
        return None

    return split_two
